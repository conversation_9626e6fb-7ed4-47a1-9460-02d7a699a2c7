/*                                                $$$$$$$$$$$$$$$$$$   IMPORTANT NOTES    $$$$$$$$$$$$$$$$$$


How to Use:
  1. Plug in battery. (Make sure to charge when done, chargers are on the right side <PERSON>'s desk near the Cube)
  2. Turn on power by flipping switch.
    - Be seated in wheelchair before you flip the switch, don't be applying any force to wheel rim. 
    - Ensure SD card is inserted inside SD Card Breakout Board.
    - Green LED will blink three times to indicate successful setup.
  3. When you are ready to start your trial, push the Start Button.
    - At that moment the Green LED will turn on and there will be a flashing Red light from the SD Card Breakout Board to indicate data is being logged.
  4. When you want to end you trial push the Stop Button and then you can either turn off the monitor by flipping the switch or you can immediately start another trial by pushing the Start Button. 
  5. When your done using TheSmartWheel, take the battery out and charge it. 


1. Calibration

  Follow these steps to calibrate the strain gauges:
  0. Copy and paste the code to a new Arduino sketch and then make these following changes.
  1. Ensure that in the void setup() that this line is commented out: scale1.set_scale(factor1); 
  2. In the void loop, change .get_units(3) to .get_units(10), Ex: float appliedForce1 = scale1.get_units(3);  CHANGE TO --> float appliedForce1 = scale1.get_units(10);  Do for all scale's
  3. Instead of logging the force value you need to log the raw values.  dataFile.print(appliedForceN5);dataFile.print(",");  CHANGE TO -->  dataFile.print(appliedForce5);dataFile.print(","); Do for all.
  4. Once you upload the code, you can now start calibrating. 
  
  To calibrate a tangential force follow these steps:
    1. Attach the rod and hook to the Mecmesin Device
    2. Turn on TheSmartWheel, don't touch or do anything to it when you first turn it on because it will mess with the tare. (The green LED should blink three times to indicate a succesful setup)
    3. Press the start button and wait 5-10 seconds, right now you are recording the strain gauge's raw values when no force is applied. (When you press the start button, a green LED should turn on and stay on, The SD card module should be blinking rapidly too)
    4. Now attach the hook to the top of the metal rim and apply a force of 5 Newtons, hold as steady as you can for 10 seconds.
    5. After the 10 seconds take the hook off and wait another 5-10 seconds.
    6. Attach the hook again and now apply 10 Newtons
    7. Repeat and keep incrementing your applied force. I suggest going up by 5 Newtons.
    8. After the 55 Newtons you can either stop or keep going. (what we did was jump to 70 Newtons after the 55)

  Now you can Stop the trial by pushing the stop button. Take out the SD card and open the CSV on your laptop.
  You now have to analyze the data. Find the corresponding coloumn of the strain gauge your calibrating. The first 'chunk' of data you will see corresponds to the zero newton force, copy and paste those values on a new excel sheet and title the coloum as '0 Newtons'.
  Go through the coloumn and extract the data for each force you applied. Since you removed the force after each incrementation and paused for a couple seconds it will be easy to identify each 'chunk' of raw values that corresponds to the applied force.
  Be patient and take your time when extracting the data. It will seem overwhelming but, you'll get the hang of it. 
  You should now have a excel file that has columns from 0 Newtons to wherever you stopped at ex. 70 Newtons. 
  Create two new columns one titled Voltage the other Force
  You can fill the Force coloumn with the Mecmesin output. Ex row 1 should be 0, row 2 should 5, row 3 should be 10 and so on.
  For the first row of the voltage column take the average of your 0 Newton coloumn.
  The next is the average of the 5 Newton coloumn and so on.

  Finally, create a scatter plot. Ensure: Force is on the Y-axis, Voltage on the X-axis. Add Trendline and Trendline Equation.
  The Trendline Equation is the Calibration Equation, implement in the code and test its accuracy by applying the same forces you did to get the raw values.

  To calibrate a Radial Force do the same things but instead of hook attachment use the round flat attactment. Applly the force above the beam below the rim.

  If you have gone through the code you may have noticed that Beam2Tangential doesn't have a Calibration Equation. It has a Calibration Factor that was found following this tutorial: https://randomnerdtutorials.com/arduino-load-cell-hx711/

2. Wirring

  If something isn't working its almost always a wirring issue. 

  NOTE: Before soldering you have to remove all the connections, DON'T pull by the wires, you have to slowly wiggle the white connectors.
        Each group of wires are numbered, they correspond to the scale (HX711) on the PCB, make sure they go to the right ones, A test you can do to ensure you have wired things correctly is to apply tangential and radial forces one at a time on each beam and make sure that the 
        corresponding force on the csv captures it. 

  If Power Switch isn't working: 
    Take the case off TheSmartWheel carefully, and see if the black or red wire is still attached to the switch, if its not you have to carefully unplug all of the connectors and re-solder it back.   
    If the solder joints are still connected for the Power Switch, take out the battery and charge it.

  If the Green LED isn't working during setup check and see if the SD card is actually in the SD card breakout board. It won't finish the setup and blink three times if its not. If its still not working it might just be in a weird postition, check and see if a wire is wrapped around it.
  Worst case scenerio is that you have to resolder a new LED, ensure the the long leg of the led is soldered to the left side of the connector (positive) or just check the schematic on EasyEDA, you may have to ask Martin to send it to you.

  If one of the blue wires are out of the white connector, you need to strip off a little bit of it and re-crimp it. Watch a youtube video on crimping and practice a couple times before you do it on TheSmartWheel

  To test the connections do the 'beep' test using the multimeter, verify that the connection corresponds to the PCB routing, which can be found on EasyEDA, SmartWheel project, 4thIteration
  
*/
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <Arduino.h>
#include <SPI.h>
#include <SD.h>
#include <HX711.h>
#include <math.h>

unsigned long trialStartTime = 0;
unsigned long lastTime = 0;


///////////////////////////////////////////////////////
// ************SD Card Variables************///////////
///////////////////////////////////////////////////////
File dataFile;
int trialNumber = 0;
String fileName = "";

// CD pin on module goes to D34
#define CS_PIN    32 //CS on module
#define MOSI_PIN  13 // D1 on module
#define MISO_PIN  14 // D0 on module
#define SCK_PIN   12 // SCK on module

SPIClass spi = SPIClass(VSPI);  // VSPI = default SPI hardware peripheral

///////////////////////////////////////////////////////
// ************TANGENTIAL VARIBALES************////////
///////////////////////////////////////////////////////

const int LOADCELL_SCK_PIN = 18; // SCK Pin for HX711's

const int LOADCELL_DOUT_PIN = 15; //    Beam 2    Bottom right
const int LOADCELL3_DOUT_PIN = 4; //    Beam 3    Top
const int LOADCELL5_DOUT_PIN = 23; //   Beam 1    Bottom left

HX711 scale1;
HX711 scale3; 
HX711 scale5; 


//////////////////////////////////////////////////////
// ************RADIAL VARIBALES************///////////
//////////////////////////////////////////////////////
const int LOADCELL2_DOUT_PIN = 2;
const int LOADCELL4_DOUT_PIN = 5;
const int LOADCELL6_DOUT_PIN = 22;

HX711 scale2; 
HX711 scale4; 
HX711 scale6; 

///////////////////////////////////////////////////////
// ************Button Variables************////////////
///////////////////////////////////////////////////////
int startButton = 35;
int startVal = 0;
int stopButton = 33;
int stopVal = 0;
int prevStopVal = 0;
int prevStartVal = 0;

///////////////////////////////////////////////////////
// ************LED Variables************///////////////
///////////////////////////////////////////////////////
int orangeLed = 25;
int greenLed = 26;

///////////////////////////////////////////////////////
// ************Logging Variables************///////////
///////////////////////////////////////////////////////
bool isLogging = false;

///////////////////////////////////////////////////////
// ************       SETUP       **********///////////
///////////////////////////////////////////////////////
void setup() {
  
  Serial.begin(115200);

  pinMode(startButton, INPUT);
  pinMode(stopButton, INPUT);
  //pinMode(orangeLed, OUTPUT);
  pinMode(greenLed, OUTPUT);

  spi.begin(SCK_PIN, MISO_PIN, MOSI_PIN, CS_PIN);  // sck, miso, mosi, cs
  delay(2000);  // give SD module time to power up

  if (!SD.begin(CS_PIN, spi)) {
    Serial.println("SD Card Mount Failed");
    //digitalWrite(orangeLed,HIGH);
    delay(2000);  // Let LED and serial message show
    ESP.restart();  
  }
  Serial.println("SD Card initialized.");
  
 
  pinMode(LOADCELL_SCK_PIN, OUTPUT);  
  pinMode(LOADCELL_DOUT_PIN, INPUT); 
  pinMode(LOADCELL2_DOUT_PIN, INPUT);
  pinMode(LOADCELL3_DOUT_PIN, INPUT);
  pinMode(LOADCELL4_DOUT_PIN, INPUT); 
  pinMode(LOADCELL5_DOUT_PIN, INPUT);
  pinMode(LOADCELL6_DOUT_PIN, INPUT);

// Connect to the force sensor breakout board
  scale1.begin(LOADCELL_DOUT_PIN, LOADCELL_SCK_PIN);  
  scale2.begin(LOADCELL2_DOUT_PIN, LOADCELL_SCK_PIN);
  scale3.begin(LOADCELL3_DOUT_PIN, LOADCELL_SCK_PIN);
  scale4.begin(LOADCELL4_DOUT_PIN, LOADCELL_SCK_PIN);
  scale5.begin(LOADCELL5_DOUT_PIN, LOADCELL_SCK_PIN);
  scale6.begin(LOADCELL6_DOUT_PIN, LOADCELL_SCK_PIN);


// Reset the scale to zero.
  scale1.tare();  
  scale2.tare();
  scale3.tare();
  scale4.tare();
  scale5.tare();
  scale6.tare();

  for(int i = 0; i<3;i++){
    digitalWrite(greenLed,HIGH);
    delay(500);
    digitalWrite(greenLed,LOW);
    delay(500);
  }

}

///////////////////////////////////////////////////////
// ************       LOOP        **********///////////
///////////////////////////////////////////////////////
void loop() {

  startVal = digitalRead(startButton);
  stopVal = digitalRead(stopButton);

  if(!isLogging && startVal == 1 && prevStartVal == 0){
    
    trialStartTime = millis();

    trialNumber = getTrialNumber();
    fileName = "/T" + String(trialNumber) + ".CSV";
    Serial.println("File to write: " + fileName);
    dataFile = SD.open(fileName.c_str(), FILE_APPEND);
    
    if (dataFile) {
      // CSV header 
      dataFile.println("Time,Beam1Tangential,Beam1Radial,Beam2Tangential,Beam2Radial,Beam3Tangential,Beam3Radial");
      dataFile.flush();  // Make sure header is written
      Serial.println("File opened for logging.");

      digitalWrite(greenLed, HIGH);
      //digitalWrite(orangeLed,LOW);
      isLogging = true;
    } 
    
    else{
      Serial.print("Error opening file!");
      digitalWrite(greenLed,LOW);
      //digitalWrite(orangeLed,HIGH);
      return;
    }
  }

  if(isLogging && stopVal == 1 && prevStopVal == 0){
    Serial.println("Logging Stopped!");
    digitalWrite(greenLed,LOW);
    if(dataFile){
      dataFile.close();
      //digitalWrite(orangeLed,HIGH);
    }
    isLogging = false;
  }

  prevStartVal = startVal;
  prevStopVal = stopVal;

  digitalWrite(greenLed, isLogging ? HIGH : LOW);
  //digitalWrite(orangeLed,isLogging ? LOW : HIGH); 

  if(isLogging){
    // HX711 voltage
    float appliedForce1 = scale1.get_units(10); 
    float appliedForce2 = scale2.get_units(10); 
    float appliedForce3 = scale3.get_units(10);
    float appliedForce4 = scale4.get_units(10); 
    float appliedForce5 = scale5.get_units(10); 
    float appliedForce6 = scale6.get_units(10); 
    

     if (dataFile) {
      digitalWrite(greenLed, HIGH);
      //digitalWrite(orangeLed,LOW);
      float t = (millis() - trialStartTime) / 1000.0f; // Reseting the time to zero
      dataFile.print(t,3); dataFile.print(","); // Time
      //dataFile.print(enVelocity, 6); dataFile.print(",");
      dataFile.print(appliedForce5);dataFile.print(","); // Beam 1 Tangential
      dataFile.print(appliedForce6);dataFile.print(","); // Beam 1 Radial
      dataFile.print(appliedForce1);dataFile.print(","); // Beam 2 Tangential
      dataFile.print(appliedForce2);dataFile.print(","); // Beam 2 Radial
      dataFile.print(appliedForce3);dataFile.print(","); // Beam 3 Tangential
      dataFile.println(appliedForce4);                     // Beam 3 Radial 
      dataFile.flush();
      } 
      else {
        Serial.println("⚠️ Data file is not open!");
      }
  }
}

int getTrialNumber() {
  int num = 1;
  File indexFile;
  if (SD.exists("/index.txt")) {
    indexFile = SD.open("/index.txt", FILE_READ);
    if (indexFile) {
      String content = indexFile.readStringUntil('\n');
      num = content.toInt() + 1;
      indexFile.close();
    }
  }
  indexFile = SD.open("/index.txt", FILE_WRITE);
  if (indexFile) {
    indexFile.println(num);
    indexFile.close();
  }
  return num;
}