// #include <Arduino.h>
// #include <SPI.h>
// #include <HX711.h>

// const int LOADCELL_DOUT_PIN = 11;
// const int LOADCELL_SCK_PIN = 13;

// float appliedpress;               // FSR values
// float appliedforce;               // Calibrated sensor in Kg
// float appliedforceN;              // Calibrated sensor in N
// float threshangvel = 5.0;
// float forcetrigger = 0; // Set this threshold to an appropriate value in N
// float ZeroForce=0;
// int forceflag = 0; // Set the force flag to zero at start
// long readingsum=0;
// long readingoffset=0;
// long readingmean=0;
// long reading;
// int m=0;

// HX711 scale;  // Initializes the force sensor circuit

// //**************************  FORCE READING  **************************
// void forcereading() {
//     reading = scale.read();  // Get a fresh reading each loop cycle


//     appliedpress =  (reading - readingoffset);  // On this sensor the readings are negative due to wiring setup readingoffset is not used but left in
//     Serial.print("Raw Force Signal: ");Serial.println(appliedpress);

//     //LEFT CALIBRATION DATA
  
//     appliedforce = (0.0000224*appliedpress+0.2980)-ZeroForce; // June 21 using force gauge in KG  Offset already removed
    
//     appliedforceN = (appliedforce*9.81) +107.26;        // Applied force in N. Back to N again.
//     Serial.print("Applied Force N: ");Serial.println(appliedforceN);
// }

// //**************************  Setup  **************************

// void setup() {
//   pinMode(13, OUTPUT);                        // The clock pulse to force sensor breakout circuit
//   pinMode(11,INPUT);                          // Digital input from force sensor breakout circuit

//   Serial.begin(57600);

//   scale.begin(LOADCELL_DOUT_PIN, LOADCELL_SCK_PIN); // Makes the connection to the Force sensing breakout board

//   if (scale.is_ready()) {
  
//   // To remove the offset  takes 4s
//   while (m <= 20) {
//     reading = (scale.read()); // If output is negative
//     readingsum=readingsum + reading;
//     delay(100);
//     m++; 
//   }
    
//   readingoffset = readingsum/m;
    
//   Serial.print("HX711 Offset: ");
//   Serial.println(readingoffset);

//   reading = scale.read();  // Get a zero force reading
//   appliedpress =  (reading - readingoffset);  // On this sensor the readings are negative due to wiring setup readingoffset is not used but left in    

//   appliedforce = (0.00014417*appliedpress + 0.2980); // May 3 using force gauge in N
//   ZeroForce = appliedforce;  // This is in N

//   Serial.println("ZeroForce N:");  Serial.println(ZeroForce);    //  Only on monitor
//   Serial.println("Reading Offset:");  Serial.println(readingoffset);    //  Only on monitor

//   delay(3000); // Add a delay here so that the offset and measurement are separated to get ready
//   }
// }

// //**************************  Main Loop  **************************
// void loop() {
//   Serial.print("HX711 Offset: ");
//   Serial.println(readingoffset);
//   forcereading();

//   delay(150);  // Set sampling rate as fast a possible
// }