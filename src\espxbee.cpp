// /*
//  * ESP32 XBee Incrementing Counter
//  * 
//  * This program sends an incrementing counter value wirelessly using an XBee module
//  * The counter increments on each loop iteration and transmits the value via XBee
//  * 
//  * Hardware:
//  * - ESP32 development board
//  * - XBee module (S1 or S2) connected to ESP32 via UART
//  * 
//  * Connections:
//  * - ESP32 TX2 (GPIO17) -> XBee RX (DIN)
//  * - ESP32 RX2 (GPIO16) -> XBee TX (DOUT)
//  * - ESP32 3.3V -> XBee 3.3V
//  * - ESP32 GND -> XBee GND
//  */

//  #include <XBee.h>

//  // XBee setup
//  XBee xbee = XBee();
//  uint8_t payload[12]; // Payload buffer
//  uint16_t counter = 0; // Counter to increment and send
//  const uint16_t DESTINATION_ADDRESS = 0x8888; // Address of the receiving XBee (modify as needed)
 
//  // Create a TX request with the payload
//  Tx16Request tx = Tx16Request(DESTINATION_ADDRESS, payload, sizeof(payload));
 
//  // For tracking transmission status
//  TxStatusResponse txStatus = TxStatusResponse();
 
//  // LED pin for visual feedback
//  const int LED_PIN = 2; // Built-in LED on many ESP32 dev boards
 
//  void setup() {
//    // Initialize serial communication with the computer for debugging
//    Serial.begin(115200);
//    delay(100);
//    Serial.println("ESP32 XBee Counter - Starting up");
   
//    // Initialize serial communication with XBee
//    Serial2.begin(57600); // Use Serial2 on ESP32 for XBee communication
//    xbee.setSerial(Serial2);
   
//    // Set LED pin as output
//    pinMode(LED_PIN, OUTPUT);
   
//    // Flash LED to indicate successful initialization
//    for (int i = 0; i < 3; i++) {
//      digitalWrite(LED_PIN, HIGH);
//      delay(200);
//      digitalWrite(LED_PIN, LOW);
//      delay(200);
//    }
   
//    Serial.println("Setup complete - starting transmission loop");
//  }
 
//  void loop() {
//    // Increment counter
//    counter++;
   
//    // Flash LED to indicate transmission
//    digitalWrite(LED_PIN, HIGH);
   
//    // Prepare payload with header and counter value
//    payload[0] = 0x7F; // Header byte 1 (same as in example)
//    payload[1] = 0x7F; // Header byte 2 (same as in example)
   
//    // Include counter value (16-bit, split into 2 bytes)
//    payload[2] = (counter >> 8) & 0xFF; // High byte
//    payload[3] = counter & 0xFF;        // Low byte
   
//    // Fill remaining bytes with zeros or additional data if needed
//    for (int i = 4; i < sizeof(payload); i++) {
//      payload[i] = 0;
//    }
   
//    // Send the data
//    xbee.send(tx);
   
//    // Print debug information
//    Serial.print("Sending counter value: ");
//    Serial.println(counter);
   
//    // Turn off LED
//    digitalWrite(LED_PIN, LOW);
   
//    // Wait for a moment before sending next value
//    delay(1000);
   
//    // Reset counter if it reaches maximum value to prevent overflow
//    if (counter == 65535) {
//      counter = 0;
//    }
//  }