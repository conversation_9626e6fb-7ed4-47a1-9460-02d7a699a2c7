#!/usr/bin/env python3
import serial
from xbee import XBee
import signal
import sys
import time

# Serial parameters - update with your port
SERIAL_PORT = "COM7"  # Change to your XBee serial port (e.g., "/dev/ttyUSB0" on Linux)
BAUDRATE = 57600

# Global variables for clean shutdown
running = True
serial_conn = None
xbee = None

def signal_handler(sig, frame):
    """Handle keyboard interrupts (Ctrl+C)"""
    print("\nReceived keyboard interrupt, stopping gracefully...")
    stop()
    sys.exit(0)

def connect_serial():
    """Connect to the XBee module through serial port"""
    global serial_conn, xbee
    
    try:
        print(f"Attempting to connect to XBee on {SERIAL_PORT}...")
        serial_conn = serial.Serial(
            port=SERIAL_PORT,
            baudrate=BAUDRATE,
            bytesize=8,
            timeout=5
        )
        xbee = XBee(serial_conn)
        print("Serial connection established.")
        return True
    except Exception as e:
        print(f"Error connecting to serial port: {e}")
        return False

def stop():
    """Close serial connection and exit gracefully"""
    global running, serial_conn
    
    running = False
    
    if serial_conn:
        try:
            serial_conn.close()
            print("Serial connection closed.")
        except Exception as e:
            print(f"Error closing serial connection: {e}")

def main():
    """Main function to receive and display XBee counter data"""
    global running
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Connect to XBee
    if not connect_serial():
        print("Failed to connect to XBee. Exiting.")
        return
    
    print("Listening for XBee counter data... (Press Ctrl+C to exit)")
    
    # Main loop to receive data
    previous_counter = None
    while running:
        try:
            # Wait for a packet from XBee
            raw_data = xbee.wait_read_frame()
            data = raw_data["rf_data"]
            
            # Convert to hex for easier processing
            hex_data = data.hex()
            data_bytes = [hex_data[i:i+2] for i in range(0, len(hex_data), 2)]
            data_decimals = [int(byte, 16) for byte in data_bytes]
            
            # Check for valid packet (at least 4 bytes with correct header)
            if len(data_decimals) < 4 or data_decimals[0] != 0x7F or data_decimals[1] != 0x7F:
                print("Invalid packet format: Header mismatch or incomplete data")
                continue
                
            # Extract counter value (bytes 2-3)
            counter = (data_decimals[2] << 8) | data_decimals[3]
            
            # Print counter value (show if it changed)
            if counter != previous_counter:
                timestamp = time.strftime("%H:%M:%S")
                print(f"[{timestamp}] Received counter value: {counter}")
                previous_counter = counter
            
        except Exception as e:
            if running:  # Only show error if we're still supposed to be running
                print(f"Error reading XBee data: {e}")
                time.sleep(1)  # Prevent error flooding

if __name__ == "__main__":
    main()