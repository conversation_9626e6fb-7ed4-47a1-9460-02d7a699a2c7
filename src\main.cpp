#include <Arduino.h>
#include <SPI.h>
#include <SD.h>
#include <HX711.h>
//#include <Encoder.h>

// Load cell pins
//working pins 18, 19, 5, 21, 22, 23, 2, 4, 15
const int LOADCELL_DOUT_PIN = 15;
const int LOADCELL2_DOUT_PIN = 2;
const int LOADCELL3_DOUT_PIN = 4;
const int LOADCELL4_DOUT_PIN = 5;
//const int LOADCELL5_DOUT_PIN = 19
//const int LOADCELL6_DOUT_PIN = 21;
const int LOADCELL_SCK_PIN = 18;

// Force sensor variables
float appliedpress;
float appliedforce;
float appliedforceN;
float ZeroForce = 0;
long reading;
const long LOADCELL_DIVIDER = 1605055;

HX711 scale1;  // Initializes the force sensor circuit
HX711 scale2;  // Initializes the second force sensor circuit
HX711 scale3;  // Initializes the third force sensor circuit
HX711 scale4;  // Initializes the fourth force sensor circuit
//HX711 scale5;  // Initializes the fifth force sensor circuit
//HX711 scale6;  // Initializes the sixth force sensor circuit

HX711* scaleList[] = {&scale1, &scale2, &scale3, &scale4};


// Calibration factor
const float CALIBRATION_FACTOR = 0.0000224;
const float OFFSET = 0.2980;  // This should be obtained by calibration

// Encoder pins
// Encoder myEnc(9, 6);
// long oldPosition  = -999;
// long newPosition = 0;

// Force reading interval (12.5 ms for 80 Hz)
const unsigned long forceInterval = 100;
unsigned long previousMillis = 0;

// Function to read force
struct ForceReading {
    unsigned long currentMillis;
    unsigned long timeBetweenSamples;
    long newPosition;
    float appliedPress;
    float appliedForceN;
};

ForceReading forcelisting[4];

void forcereading() {
    unsigned long currentMillis = millis();

    for (int i = 0; i < 4; i++) {
        if (scaleList[i]->is_ready()) {
            forcelisting[i].appliedPress = scaleList[i]->read();
            forcelisting[i].appliedForceN = (CALIBRATION_FACTOR * forcelisting[i].appliedPress + OFFSET) - ZeroForce;
            forcelisting[i].appliedForceN = (forcelisting[i].appliedForceN * 9.81) + 107.26;

            forcelisting[i].currentMillis = currentMillis;
            forcelisting[i].timeBetweenSamples = currentMillis - previousMillis;
        } else {
            Serial.print("Error: HX711 #");
            Serial.print(i);
            Serial.println(" not ready");
        }
    }


    // float appliedpress = 1 - scale1.get_offset();  // Adjust reading offset
    // float appliedforce = (CALIBRATION_FACTOR * appliedpress + OFFSET) - ZeroForce; // Convert to KG
    // float appliedforceN = (appliedforce * 9.81) + 107.26;  // Convert to N

    // // Calculate the time between samples
    // unsigned long currentMillis = millis();
    // unsigned long timeBetweenSamples = currentMillis - previousMillis;
    // previousMillis = currentMillis;

    // // Prepare the structure with the data
    // readingData.currentMillis = currentMillis;
    // readingData.timeBetweenSamples = timeBetweenSamples;
    // //readingData.newPosition = myEnc.read();
    // readingData.appliedPress = appliedpress;
    // readingData.appliedForceN = appliedforceN;

}

void setup() {
    // Initialize serial communication
    Serial.begin(115200);

    // Load cell setup
    pinMode(LOADCELL_SCK_PIN, OUTPUT);  
    pinMode(LOADCELL_DOUT_PIN, INPUT); 
    pinMode(LOADCELL2_DOUT_PIN, INPUT);
    pinMode(LOADCELL3_DOUT_PIN, INPUT);
    pinMode(LOADCELL4_DOUT_PIN, INPUT); 

    scale1.begin(LOADCELL_DOUT_PIN, LOADCELL_SCK_PIN);  // Connect to the force sensor breakout board
    scale2.begin(LOADCELL2_DOUT_PIN, LOADCELL_SCK_PIN);
    scale3.begin(LOADCELL3_DOUT_PIN, LOADCELL_SCK_PIN);
    scale4.begin(LOADCELL4_DOUT_PIN, LOADCELL_SCK_PIN);
    scale1.set_scale(LOADCELL_DIVIDER);
    scale2.set_scale(LOADCELL_DIVIDER);
    scale3.set_scale(LOADCELL_DIVIDER);
    scale4.set_scale(LOADCELL_DIVIDER);
    scale1.tare();  // Reset the scale to zero
    scale2.tare();
    scale3.tare();
    scale4.tare();

    // Initial print statements
    Serial.println("Timestamp (ms),Time Between Samples (ms),Encoder Position,Raw Force Signal,Applied Force N");
}

void loop() {
    unsigned long currentMillis = millis();
    unsigned long interval = currentMillis - previousMillis;


    
    if (interval >= forceInterval) {
        forcereading();

        for ( int i = 0; i < 4; i++) {
                Serial.print(forcelisting[i].currentMillis);
                Serial.print(",");
                Serial.print(forcelisting[i].timeBetweenSamples);
                Serial.print(",");
                //Serial.print(forcelisting[i].newPosition);
                //Serial.print(",");
                Serial.print(forcelisting[i].appliedPress);
                Serial.print(",");
                Serial.println(forcelisting[i].appliedForceN);
        }




        // ForceReading forceData = forcereading();
        
        // Serial.print(forceData.currentMillis);
        // Serial.print(",");
        // Serial.print(forceData.timeBetweenSamples);
        // Serial.print(",");
        // //Serial.print(forceData.newPosition);
        // //Serial.print(",");
        // Serial.print(forceData.appliedPress);
        // Serial.print(",");
        // Serial.println(forceData.appliedForceN);
        
        previousMillis = currentMillis;
    }
}

// /*
//  * ESP32 Drive Shaft Distance Measurement with Encoder
//  * 
//  * This sketch reads a rotary encoder connected to a drive shaft on an ESP32.
//  * It calculates and displays the total distance traveled in both directions.
//  * 
//  * Connections:
//  * - Encoder pin A to GPIO 23
//  * - Encoder pin B to GPIO 22
//  * - Encoder button (if present) to GPIO 21
//  * - Encoder VCC to 3.3V
//  * - Encoder GND to GND
//  */

// #include <Arduino.h>
// #include <SPI.h>

// /*
//  * ESP32 Multiple Strain Gauge Reader with HX711 Amplifiers and Manual Encoder
//  * 
//  * This sketch reads values from 6 strain gauges connected via HX711 amplifiers
//  * and a rotary encoder (manually implemented), outputting measurements to the serial port.
//  */

//  #include "HX711.h"

//  // Define pins for each HX711
//  #define DOUT1 16
//  #define SCK1 17
//  #define DOUT2 18
//  #define SCK2 19
//  #define DOUT3 21
//  #define SCK3 22
//  #define DOUT4 23
//  #define SCK4 25
//  #define DOUT5 26
//  #define SCK5 27
//  #define DOUT6 32
//  #define SCK6 33
 
//  // Encoder pins
//  #define ENCODER_PIN_A 9
//  #define ENCODER_PIN_B 6
 
//  // Create HX711 objects
//  HX711 scale1;
//  HX711 scale2;
//  HX711 scale3;
//  HX711 scale4;
//  HX711 scale5;
//  HX711 scale6;
 
//  // Force sensor variables (from original code)
//  float appliedPress[6];
//  float appliedForce[6];
//  float appliedForceN[6];
//  float zeroForce[6] = {0, 0, 0, 0, 0, 0};
//  long reading[6];
 
//  // Calibration factors 
//  const float CALIBRATION_FACTOR = 0.0000224;
//  const float OFFSET = 0.2980;
 
//  // Force reading interval (12.5 ms for 80 Hz)
//  const unsigned long forceInterval = 12.5;
//  unsigned long previousMillis = 0;
 
//  // Manual encoder implementation variables
//  volatile long encoderPosition = 0;
//  volatile uint8_t lastEncoded = 0;
//  volatile uint8_t encoderState = 0;
 
//  // Structure for force readings, including encoder position
//  struct ForceReading {
//      unsigned long currentMillis;
//      unsigned long timeBetweenSamples;
//      long encoderPosition;
//      float appliedPress;
//      float appliedForceN;
//  };
 
//  // Function to manually handle encoder interrupts
//  void IRAM_ATTR handleEncoder() {
//      // Read current state of both pins
//      uint8_t MSB = digitalRead(ENCODER_PIN_A);
//      uint8_t LSB = digitalRead(ENCODER_PIN_B);
     
//      // Convert the 2 pin readings to a single number 0-3
//      uint8_t encoded = (MSB << 1) | LSB;
     
//      // Process based on previous and current state
//      uint8_t sum = (lastEncoded << 2) | encoded;
     
//      // Determine direction based on state transition
//      if(sum == 0b0001 || sum == 0b0111 || sum == 0b1110 || sum == 0b1000) {
//          encoderPosition++;
//      } else if(sum == 0b0010 || sum == 0b1011 || sum == 0b1101 || sum == 0b0100) {
//          encoderPosition--;
//      }
     
//      // Save current state for next time
//      lastEncoded = encoded;
//  }
 
//  // Read force from the primary scale
//  ForceReading readForce() {
//      ForceReading readingData;
     
//      // Read from primary scale (scale1) - using similar calculations as original code
//      float reading = scale1.read();
//      float appliedPress = reading - scale1.get_offset();
//      float appliedForce = (CALIBRATION_FACTOR * appliedPress + OFFSET) - zeroForce[0];
//      float appliedForceN = (appliedForce * 9.81) + 107.26;  // Convert to N
     
//      // Calculate time between samples
//      unsigned long currentMillis = millis();
//      unsigned long timeBetweenSamples = currentMillis - previousMillis;
     
//      // Prepare the structure with the data
//      readingData.currentMillis = currentMillis;
//      readingData.timeBetweenSamples = timeBetweenSamples;
//      readingData.encoderPosition = encoderPosition;
//      readingData.appliedPress = appliedPress;
//      readingData.appliedForceN = appliedForceN;
     
//      return readingData;
//  }
 
//  void setup() {
//      // Initialize serial communication
//      Serial.begin(57600);
     
//      // Initialize all HX711 scales
//      scale1.begin(DOUT1, SCK1);
//      scale2.begin(DOUT2, SCK2);
//      scale3.begin(DOUT3, SCK3);
//      scale4.begin(DOUT4, SCK4);
//      scale5.begin(DOUT5, SCK5);
//      scale6.begin(DOUT6, SCK6);
     
//      // Using original code's scale divider
//      const long LOADCELL_DIVIDER = 1605055;
//      scale1.set_scale(LOADCELL_DIVIDER);
//      scale2.set_scale(LOADCELL_DIVIDER);
//      scale3.set_scale(LOADCELL_DIVIDER);
//      scale4.set_scale(LOADCELL_DIVIDER);
//      scale5.set_scale(LOADCELL_DIVIDER);
//      scale6.set_scale(LOADCELL_DIVIDER);
     
//      // Tare all scales
//      Serial.println("Taring scales... remove any weights from the load cells");
//      delay(2000);
//      scale1.tare();
//      scale2.tare();
//      scale3.tare();
//      scale4.tare();
//      scale5.tare();
//      scale6.tare();
     
//      // Set up encoder pins
//      pinMode(ENCODER_PIN_A, INPUT_PULLUP);
//      pinMode(ENCODER_PIN_B, INPUT_PULLUP);
     
//      // Read initial encoder state
//      lastEncoded = (digitalRead(ENCODER_PIN_A) << 1) | digitalRead(ENCODER_PIN_B);
     
//      // Set up interrupts for encoder pins
//      attachInterrupt(digitalPinToInterrupt(ENCODER_PIN_A), handleEncoder, CHANGE);
//      attachInterrupt(digitalPinToInterrupt(ENCODER_PIN_B), handleEncoder, CHANGE);
     
//      // Print header (using original format)
//      Serial.println("Timestamp (ms),Time Between Samples (ms),Encoder Position,Raw Force Signal,Applied Force N,Force2,Force3,Force4,Force5,Force6");
     
//      // Initialize time tracking
//      previousMillis = millis();
//  }
 
//  void loop() {
//      unsigned long currentMillis = millis();
//      unsigned long interval = currentMillis - previousMillis;
     
//      // Check if it's time to read and output data (80Hz)
//      if (interval >= forceInterval) {
//          // Get force readings from main scale with timing and encoder data
//          ForceReading forceData = readForce();
         
//          // Read data from all six scales
//          if (scale1.is_ready() && scale2.is_ready() && scale3.is_ready() && 
//              scale4.is_ready() && scale5.is_ready() && scale6.is_ready()) {
             
//              // Read values from each scale (for scales 2-6)
//              float force2 = scale2.get_units(1);
//              float force3 = scale3.get_units(1);
//              float force4 = scale4.get_units(1);
//              float force5 = scale5.get_units(1);
//              float force6 = scale6.get_units(1);
             
//              // Apply calibration similar to primary scale
//              float appliedForceN2 = (CALIBRATION_FACTOR * force2 + OFFSET - zeroForce[1]) * 9.81 + 107.26;
//              float appliedForceN3 = (CALIBRATION_FACTOR * force3 + OFFSET - zeroForce[2]) * 9.81 + 107.26;
//              float appliedForceN4 = (CALIBRATION_FACTOR * force4 + OFFSET - zeroForce[3]) * 9.81 + 107.26;
//              float appliedForceN5 = (CALIBRATION_FACTOR * force5 + OFFSET - zeroForce[4]) * 9.81 + 107.26;
//              float appliedForceN6 = (CALIBRATION_FACTOR * force6 + OFFSET - zeroForce[5]) * 9.81 + 107.26;
             
//              // Print data in CSV format following original code's format
//              Serial.print(forceData.currentMillis);
//              Serial.print(",");
//              Serial.print(forceData.timeBetweenSamples);
//              Serial.print(",");
//              Serial.print(forceData.encoderPosition);
//              Serial.print(",");
//              Serial.print(forceData.appliedPress);
//              Serial.print(",");
//              Serial.print(forceData.appliedForceN);
//              Serial.print(",");
//              Serial.print(appliedForceN2, 3);
//              Serial.print(",");
//              Serial.print(appliedForceN3, 3);
//              Serial.print(",");
//              Serial.print(appliedForceN4, 3);
//              Serial.print(",");
//              Serial.print(appliedForceN5, 3);
//              Serial.print(",");
//              Serial.println(appliedForceN6, 3);
//          } else {
//              Serial.println("Error: One or more HX711 modules not ready");
//          }
         
//          previousMillis = currentMillis;
//      }
//  }
 
//  // Function to recalibrate all scales
//  void recalibrate() {
//      Serial.println("Recalibrating all scales...");
     
//      scale1.tare();
//      scale2.tare();
//      scale3.tare();
//      scale4.tare();
//      scale5.tare();
//      scale6.tare();
     
//      // Reset zero force values
//      for (int i = 0; i < 6; i++) {
//          zeroForce[i] = 0;
//      }
     
//      Serial.println("Recalibration complete.");
//  }
 
//  // Process any serial commands
//  void processSerialCommands() {
//      if (Serial.available()) {
//          char command = Serial.read();
//          if (command == 't' || command == 'T') {
//              recalibrate();
//          } else if (command == 'z' || command == 'Z') {
//              // Reset encoder position to zero
//              encoderPosition = 0;
//              Serial.println("Encoder position reset to zero");
//          }
//      }
//  }


