/*                                                $$$$$$$$$$$$$$$$$$   IMPORTANT NOTES    $$$$$$$$$$$$$$$$$$


How to Use:
  1. Plug in battery. (Make sure to charge when done, chargers are on the right side <PERSON>'s desk near the Cube)
  2. Turn on power by flipping switch.
    - Be seated in wheelchair before you flip the switch, don't be applying any force to wheel rim. 
    - Ensure SD card is inserted inside SD Card Breakout Board.
    - Green LED will blink three times to indicate successful setup.
  3. When you are ready to start your trial, push the Start Button.
    - At that moment the Green LED will turn on and there will be a flashing Red light from the SD Card Breakout Board to indicate data is being logged.
  4. When you want to end you trial push the Stop Button and then you can either turn off the monitor by flipping the switch or you can immediately start another trial by pushing the Start Button. 
  5. When your done using TheSmartWheel, take the battery out and charge it. 


1. Calibration

    To calibrate upload the CALIBRATION CODE that can be found in the SmartWheel repository.

2. Wirring

  If something isn't working its almost always a wirring issue. 

  NOTE: Before soldering you have to remove all the connections, DON'T pull by the wires, you have to slowly wiggle the white connectors.
        Each group of wires are numbered, they correspond to the scale (HX711) on the PCB, make sure they go to the right ones, A test you can do to ensure you have wired things correctly is to apply tangential and radial forces one at a time on each beam and make sure that the 
        corresponding force on the csv captures it. 

  If Power Switch isn't working: 
    Take the case off TheSmartWheel carefully, and see if the black or red wire is still attached to the switch, if its not you have to carefully unplug all of the connectors and re-solder it back.   
    If the solder joints are still connected for the Power Switch, take out the battery and charge it.

  If the Green LED isn't working during setup check and see if the SD card is actually in the SD card breakout board. It won't finish the setup and blink three times if its not. If its still not working it might just be in a weird postition, check and see if a wire is wrapped around it.
  Worst case scenerio is that you have to resolder a new LED, ensure the the long leg of the led is soldered to the left side of the connector (positive) or just check the schematic on EasyEDA, you may have to ask Martin to send it to you.

  If one of the blue wires are out of the white connector, you need to strip off a little bit of it and re-crimp it. Watch a youtube video on crimping and practice a couple times before you do it on TheSmartWheel

  To test the connections do the 'beep' test using the multimeter, verify that the connection corresponds to the PCB routing, which can be found on EasyEDA, SmartWheel project, 4thIteration



3. PushForce Equations

  The goal of this project is to have an accurate pushing force output so we can validate the Sagitta. 
  Our method of calculating PushForce is based off of the logic of the orginal SmartWheel. We are breaking the strain gauge forces (radial and tangential) into their x and y componenets and taking the magnitude to get the resultant PushForce output.

  
*/
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BNO055.h>
#include <Arduino.h>
#include <SPI.h>
#include <SD.h>
#include <HX711.h>
#include <math.h>


unsigned long trialStartTime = 0;
unsigned long lastTime = 0;



///////////////////////////////////////////////////////
// ************TABLE************///////////////////////
///////////////////////////////////////////////////////

float lookupTable[36][3] = {
  {-0.00258,  0.17621,  0.845484},
  {-0.58867, -0.34258,  1.829833},
  {-0.73180, -1.16438,  2.877416},
  {-1.09675, -2.00012,  3.874458},
  {-1.22084, -3.04263,  4.954526},
  {-1.07544, -4.02844,  3.929222},
  {-0.67506, -5.09494,  6.426854},
  {-0.00458, -6.18470,  6.860723},
  {0.662738, -7.18429,  7.172024},
  {1.497093, -8.45267,  7.400920},
  {2.686092, -9.64322,  6.224598},
  {3.030602, -10.4202,  7.279036},
  {5.144125, -11.3196,  8.821125},
  {6.780465, -12.0684,  4.529767},
  {9.564792, -12.3952,  5.454688},
  {9.387386, -12.6630,  4.557500},
  {10.5286,  -12.7715,  4.912907},
  {11.42887, -12.5313,  2.658969},
  {12.43027, -11.7959,  0.505946},
  {12.86843, -11.3546,  0.285542},
  {13.14713, -10.5892, -1.913100},
  {13.41894, -9.59812, -1.806350},
  {13.34977, -8.55477, -2.775120},
  {13.02964, -7.31298, -3.726310},
  {12.75167, -6.36859, -4.298330},
  {12.29341, -5.36136, -5.550800},
  {12.36505, -4.46429, -4.855600},
  {10.61472, -3.10483, -5.171460},
  {9.388025, -1.92790, -5.194810},
  {8.001818, -1.02227, -4.974430},
  {6.774375, -0.34075, -4.586500},
  {5.452381, 0.244286, -4.033450},
  {3.932921, 0.744157, -3.230110},
  {2.808706, 0.902353, -2.902940},
  {1.794074, 0.886914, -1.495190},
  {0.902118, 0.523176, -0.369290}
};

float correctedTan1 = 0;
float correctedTan2 = 0;
float correctedTan3 = 0;

void subtractPushRim(float beam1tangential, float beam2tangential, float beam3tangential, float angle){

 float wheelAngle = fmod(angle,360.0);
 if(wheelAngle < 0){
  wheelAngle += 360.0;
 }

  int index = (int)((wheelAngle + 5)/10) % 36;

  correctedTan1 = beam1tangential - lookupTable[index][0];
  correctedTan2 = beam2tangential - lookupTable[index][1];
  correctedTan3 = beam3tangential - lookupTable[index][2];
}


///////////////////////////////////////////////////////
// ************BNO055 Variables************////////////
///////////////////////////////////////////////////////

#define SDA_PIN 16  // RX2
#define SCL_PIN 17  // TX2

TwoWire myWire(0);  // Use I2C hardware port 0
Adafruit_BNO055 bno = Adafruit_BNO055(55, 0x28, &myWire);

#define ALPHA_LPF 0.2f

// For filtering angular velocity (used in wheelSpeed)
imu::Vector<3> filteredAngVel(0, 0, 0);

// Optional helper to convert degrees/sec to radians/sec
imu::Vector<3> toRadians(imu::Vector<3> vec) {
  return imu::Vector<3>(
    vec.x() * PI / 180.0,
    vec.y() * PI / 180.0,
    vec.z() * PI / 180.0
  );
}

///////////////////////////////////////////////////////
// ************SD Card Variables************///////////
///////////////////////////////////////////////////////
File dataFile;
int trialNumber = 0;
String fileName = "";

// CD pin on module goes to D34
#define CS_PIN    32 //CS on module
#define MOSI_PIN  13 // D1 on module
#define MISO_PIN  14 // D0 on module
#define SCK_PIN   12 // SCK on module

SPIClass spi = SPIClass(VSPI);  // VSPI = default SPI hardware peripheral

///////////////////////////////////////////////////////
// ************TANGENTIAL VARIBALES************////////
///////////////////////////////////////////////////////

const int LOADCELL_SCK_PIN = 18; // SCK Pin for HX711's

const int LOADCELL_DOUT_PIN = 15; //    Beam 2    Bottom right
const int LOADCELL3_DOUT_PIN = 4; //    Beam 3    Top
const int LOADCELL5_DOUT_PIN = 23; //   Beam 1    Bottom left

HX711 scale1;
HX711 scale3; 
HX711 scale5; 

float factor1 = 118152.594;

//////////////////////////////////////////////////////
// ************RADIAL VARIBALES************///////////
//////////////////////////////////////////////////////
const int LOADCELL2_DOUT_PIN = 2;
const int LOADCELL4_DOUT_PIN = 5;
const int LOADCELL6_DOUT_PIN = 22;

HX711 scale2; 
HX711 scale4; 
HX711 scale6; 


float filteredTangential1 = 0;
float filteredTangential2 = 0;
float filteredTangential3 = 0;


///////////////////////////////////////////////////////
// ************Distance Variables************//////////
///////////////////////////////////////////////////////

#define countPerRev 2048.0 
#define wheelCircumference 1.9304 // meters
#define wheelRadius 0.3072
volatile long totalPulseCount = 0; 
float distanceTraveled = 0.0f;
float calibratedDistanceTraveled = 0.0f;

///////////////////////////////////////////////////////
// ************Encoder Variables************///////////
///////////////////////////////////////////////////////

#define UPDATE_RATE_MICROS (30000) // 20 ms
#define enPinA (21) // Channel A
#define enPinB (19) // Channel B

volatile uint32_t countPulseA = 0;
volatile uint32_t countPulseASnapshot = 0;
volatile uint8_t enDirectionFlag = 0;
volatile uint8_t enDirectionFlagSnapshot;

float enVelocity = 0;  // Velocity in rev/s

uint32_t EncoderLastTime = 0;
uint32_t EncoderCurrentTime = 0;

// Encoder Functions //

// ISR: Count pulses on channel A
void IRAM_ATTR enc1AISR() {
  countPulseA++;
  
  if (digitalRead(enPinB) == digitalRead(enPinA)) {
    totalPulseCount--;
  } else {
    totalPulseCount++;
  }

}

// ISR: Capture direction on channel B rising edge
void IRAM_ATTR enc1BISR() {
  enDirectionFlag = digitalRead(enPinA); // Raw bit used to infer encoder direction
}

///////////////////////////////////////////////////////
// ************Button Variables************////////////
///////////////////////////////////////////////////////
int startButton = 35;
int startVal = 0;
int stopButton = 33;
int stopVal = 0;
int prevStopVal = 0;
int prevStartVal = 0;

///////////////////////////////////////////////////////
// ************LED Variables************///////////////
///////////////////////////////////////////////////////
int orangeLed = 25;
int greenLed = 26;

///////////////////////////////////////////////////////
// ************Logging Variables************///////////
///////////////////////////////////////////////////////
bool isLogging = false;

///////////////////////////////////////////////////////
// ************Angle Variables************///////////
///////////////////////////////////////////////////////
const float angle120 = (2*PI)/3;
const float angle240 = (4*PI)/3;

///////////////////////////////////////////////////////
// ************       SETUP       **********///////////
///////////////////////////////////////////////////////
void setup() {
  
  Serial.begin(115200);

  pinMode(startButton, INPUT);
  pinMode(stopButton, INPUT);
  //pinMode(orangeLed, OUTPUT);
  pinMode(greenLed, OUTPUT);

  spi.begin(SCK_PIN, MISO_PIN, MOSI_PIN, CS_PIN);  // sck, miso, mosi, cs
  delay(2000);  // give SD module time to power up

  if (!SD.begin(CS_PIN, spi)) {
    Serial.println("SD Card Mount Failed");
    //digitalWrite(orangeLed,HIGH);
    delay(2000);  // Let LED and serial message show
    ESP.restart();  
  }
  Serial.println("SD Card initialized.");
  
 // digitalWrite(orangeLed,LOW);

  myWire.begin(SDA_PIN, SCL_PIN);
  myWire.setClock(100000);  // Optional: reduce speed to improve stability

  if (!bno.begin()) {
  Serial.println("⚠️ Failed to initialize BNO055! Check wiring or I2C address.");
  while (1);  // Halt execution
}
  delay(1000);

  bno.setExtCrystalUse(true);

  bno.setMode(OPERATION_MODE_NDOF);

  pinMode(enPinA, INPUT_PULLUP);
  pinMode(enPinB, INPUT_PULLUP);
  attachInterrupt(digitalPinToInterrupt(enPinA), enc1AISR, CHANGE); 
  attachInterrupt(digitalPinToInterrupt(enPinB), enc1BISR, RISING);

  pinMode(LOADCELL_SCK_PIN, OUTPUT);  
  pinMode(LOADCELL_DOUT_PIN, INPUT); 
  pinMode(LOADCELL2_DOUT_PIN, INPUT);
  pinMode(LOADCELL3_DOUT_PIN, INPUT);
  pinMode(LOADCELL4_DOUT_PIN, INPUT); 
  pinMode(LOADCELL5_DOUT_PIN, INPUT);
  pinMode(LOADCELL6_DOUT_PIN, INPUT);

// Connect to the force sensor breakout board
  scale1.begin(LOADCELL_DOUT_PIN, LOADCELL_SCK_PIN);  
  scale2.begin(LOADCELL2_DOUT_PIN, LOADCELL_SCK_PIN);
  scale3.begin(LOADCELL3_DOUT_PIN, LOADCELL_SCK_PIN);
  scale4.begin(LOADCELL4_DOUT_PIN, LOADCELL_SCK_PIN);
  scale5.begin(LOADCELL5_DOUT_PIN, LOADCELL_SCK_PIN);
  scale6.begin(LOADCELL6_DOUT_PIN, LOADCELL_SCK_PIN);

// Implement Calibration Factor
  scale1.set_scale(factor1); // Only Beam 2 Tangential requires this, others use calibration equation

// Reset the scale to zero.
  scale1.tare();  
  scale2.tare();
  scale3.tare();
  scale4.tare();
  scale5.tare();
  scale6.tare();

  for(int i = 0; i<3;i++){
    digitalWrite(greenLed,HIGH);
    delay(500);
    digitalWrite(greenLed,LOW);
    delay(500);
  }

  lastTime = millis();

  EncoderLastTime = micros();

  noInterrupts(); //Pause all interrupts
  totalPulseCount = 0; // reset 
  interrupts(); // Resume interrupts
  distanceTraveled = 0.0f;
}

///////////////////////////////////////////////////////
// ************       LOOP        **********///////////
///////////////////////////////////////////////////////
void loop() {

  startVal = digitalRead(startButton);
  stopVal = digitalRead(stopButton);

  if(!isLogging && startVal == 1 && prevStartVal == 0){
    
    trialStartTime = millis();

    trialNumber = getTrialNumber();
    fileName = "/T" + String(trialNumber) + ".CSV";
    Serial.println("File to write: " + fileName);
    dataFile = SD.open(fileName.c_str(), FILE_APPEND);
    
    if (dataFile) {
      // CSV header 
      //dataFile.println("Time,Encoder(rev/s),Beam1Tangential,Beam1Radial,Beam2Tangential,Beam2Radial,Beam3Tangential,Beam3Radial,Fx,Fy,PushForceA,PushForceTanSum,PushForceS,WheelAngle,CalibratedWheelAngle,WheelSpeed,RawDistanceTraveled,bnoAngle,CalibratedDistanceTraveled");
      dataFile.println("Time,Beam1Tangential,Beam1Radial,Beam2Tangential,Beam2Radial,Beam3Tangential,Beam3Radial,CorrectedTan1,CorrectedTan2,CorrectedTan3,bnoWheelAngle,PushForce,WheelSpeed");
      dataFile.flush();  // Make sure header is written
      Serial.println("File opened for logging.");
/*
      noInterrupts();
      totalPulseCount = 0;
      interrupts();
      distanceTraveled = 0.0f;
      calibratedDistanceTraveled = 0.0f;
*/
      digitalWrite(greenLed, HIGH);
      //digitalWrite(orangeLed,LOW);
      isLogging = true;
    } 
    
    else{
      Serial.print("Error opening file!");
      digitalWrite(greenLed,LOW);
      //digitalWrite(orangeLed,HIGH);
      return;
    }
  }

  if(isLogging && stopVal == 1 && prevStopVal == 0){
    Serial.println("Logging Stopped!");
    digitalWrite(greenLed,LOW);
    if(dataFile){
      dataFile.close();
      //digitalWrite(orangeLed,HIGH);
    }
    isLogging = false;
  }

  prevStartVal = startVal;
  prevStopVal = stopVal;

  digitalWrite(greenLed, isLogging ? HIGH : LOW);
  //digitalWrite(orangeLed,isLogging ? LOW : HIGH); 

  if(isLogging){

    imu::Vector<3> euler = bno.getVector(Adafruit_BNO055::VECTOR_EULER);
    imu::Vector<3> rawAngVel = toRadians(bno.getVector(Adafruit_BNO055::VECTOR_GYROSCOPE));

    // Low-pass filter angular velocity (X-axis assumed)
    filteredAngVel.x() = ALPHA_LPF * rawAngVel.x() + (1.0f - ALPHA_LPF) * filteredAngVel.x();

    // Compute wheel speed
    float wheelSpeed = filteredAngVel.x() * wheelRadius;

    // Get wheel angle DEGREES
    float bnoAngleX = euler.x();
    //float bnoAngleRad = bnoAngleX * PI / 180.0;
/*
    float wheelSpeed = 0.0f;
    EncoderCurrentTime = micros();
    unsigned long currentTime = millis();
    deltaTime = (currentTime - lastTime) / 1000.0f; // Convert to seconds
    lastTime = currentTime;

    if (EncoderCurrentTime - EncoderLastTime >= UPDATE_RATE_MICROS) {
    noInterrupts();
    countPulseASnapshot = countPulseA;
    countPulseA = 0;
    enDirectionFlagSnapshot = enDirectionFlag;
    interrupts();

    enVelocity = ((double)countPulseASnapshot / ((EncoderCurrentTime - EncoderLastTime) * 1e-6)) / countPerRev;
    if (!enDirectionFlagSnapshot) enVelocity *= -1;

    wheelSpeed = enVelocity * wheelCircumference;
    float totalRevolution = totalPulseCount/countPerRev;
    distanceTraveled = totalRevolution*wheelCircumference;
    calibratedDistanceTraveled = ((0.9486*distanceTraveled) - 0.3689);

    EncoderLastTime = EncoderCurrentTime;

  }
  */
    // HX711 voltage
    float appliedForce1 = scale1.get_units(3); 
    float appliedForce2 = scale2.get_units(3); 
    float appliedForce3 = scale3.get_units(3);
    float appliedForce4 = scale4.get_units(3); 
    float appliedForce5 = scale5.get_units(3); 
    float appliedForce6 = scale6.get_units(3); 
    
    //float appliedForceN5 = ((-0.00009 * appliedForce5) + 0.1039); // Beam 1 Tangential
    float appliedForceN5 = ((-0.00008 * appliedForce5) + 0.1635);
    float appliedForceN1 = -(appliedForce1 * 9.81);               // Beam 2 Tangential  NO equation is used for this one, calibration constant found by hanging weight and following tutorial listed in the notes above.
    float appliedForceN3 = ((-0.00008 * appliedForce3) + 0.9913); // Beam 3 Tangential
    float appliedForceN6 = ((0.00007 * appliedForce6) - 0.8156);  // Beam 1 Radial
    float appliedForceN2 = ((-0.00007 * appliedForce2) + 0.6514); // Beam 2 Radial
    float appliedForceN4 = ((-0.00007 * appliedForce4) - 0.1413); // Beam 3 Radial 


    // Applying low-pass filter to reduce noise and sudden spikes
    filteredTangential1 = 0.5 * appliedForceN5 + (1 - 0.5) * filteredTangential1;
    filteredTangential2 = 0.5 * appliedForceN1 + (1 - 0.5) * filteredTangential2;
    filteredTangential3 = 0.5 * appliedForceN3 + (1 - 0.5) * filteredTangential3;

    // cos() and sin() inputs need to be in radians. (2/3)*PI = 120 degrees, (4/3)*PI = 240 degrees 
  //  float FxS = ((cos(0)*appliedForceN3) + (cos(0)*appliedForceN4) + (cos(angle120)*appliedForceN1) + (cos(angle120)*appliedForceN2) + (cos(angle240)*appliedForceN5) + (cos(angle240)*appliedForceN6));
   // float FyS = ((sin(0)*appliedForceN3) + (sin(0)*appliedForceN4) + (sin(angle120)*appliedForceN1) + (sin(angle120)*appliedForceN2) + (sin(angle240)*appliedForceN5) + (sin(angle240)*appliedForceN6));

    
    subtractPushRim(filteredTangential1,filteredTangential2,filteredTangential3,bnoAngleX);

    /*
    float wheelAngle = 2.0 * PI * (totalPulseCount / countPerRev); // Converts encoder counts into wheel angle in radians
    float calibratedWheelAngle = ((wheelAngle * 180.0 / PI) + 1.7207) / 0.9656 * PI / 180.0;

    float wheelAngleDeg = wheelAngle * 180.0 / PI;
   // float calibratedAngleDeg = (wheelAngleDeg + 1.7207) / (0.9656);

   // float correctedAngleDeg = fmod(calibratedAngleDeg, 360.0);
    //if (correctedAngleDeg < 0) correctedAngleDeg += 360.0;

    // Encoder Angles
    float theta3T = calibratedWheelAngle; //Beam 3
    float theta2T = calibratedWheelAngle + angle120; // Beam 2
    float theta1T = calibratedWheelAngle + angle240; // Beam 1

    // Radial directions (tangential + 90 degrees = add PI/2.0)
    float theta3R = theta3T + (PI/2.0);
    float theta2R = theta2T + (PI/2.0);
    float theta1R = theta1T + (PI/2.0);
*/
    float angle3T = bnoAngleX; // Beam3
    float angle2T = bnoAngleX + angle120; // Beam 2
    float angle1T = bnoAngleX + angle240; // Beam 1

    float angle3R = angle3T + (PI/2.0);
    float angle2R = angle2T + (PI/2.0);
    float angle1R = angle1T + (PI/2.0);

   // float FxBno = cos(angle1T)*appliedForceN5  + cos(angle2T)*appliedForceN1 + cos(angle3T)*appliedForceN3;
   // float FyBno = sin(angle1T)*appliedForceN5 +sin(angle2T)*appliedForceN1 + sin(angle3T)*appliedForceN3;

    float correctedFx = cos(angle1T)*correctedTan1 + cos(angle2T)*correctedTan2 + cos(angle3T)*correctedTan3;
    float correctedFy = sin(angle1T)*correctedTan1 + sin(angle2T)*correctedTan2 + sin(angle3T)*correctedTan3;

    /* Beam 1 as starting position 
    float theta3T = calibratedWheelAngle + angl240; //Beam 3
    float theta2T = calibratedWheelAngle + angle120; // Beam 2
    float theta1T = calibratedWheelAngle; // Beam 1

    // Radial directions (tangential + 90 degrees = add PI/2.0)
    float theta3R = theta3T + (PI/2.0);
    float theta2R = theta2T + (PI/2.0);
    float theta1R = theta1T + (PI/2.0);
    
    */

    // Use calibrated wheel angle
    //float Fx = cos(theta1T)*appliedForceN5 + cos(theta1R)*appliedForceN6 + cos(theta2T)*appliedForceN1 + cos(theta2R)*appliedForceN2 + cos(theta3T)*appliedForceN3 + cos(theta3R)*appliedForceN4;
  //  float Fy = sin(theta1T)*appliedForceN5 + sin(theta1R)*appliedForceN6 +sin(theta2T)*appliedForceN1 + sin(theta2R)*appliedForceN2 + sin(theta3T)*appliedForceN3 + sin(theta3R)*appliedForceN4;

    // Trying with just tangential forces
    //float Fx = cos(theta1T)*appliedForceN5  + cos(theta2T)*appliedForceN1 +  cos(theta3T)*appliedForceN3;
    //float Fy = sin(theta1T)*appliedForceN5 +sin(theta2T)*appliedForceN1  + sin(theta3T)*appliedForceN3;

    // PushForce equations
    //float pushForceS = sqrt((FxS*FxS)+(FyS*FyS)); //Equation 1 using static angles
    //float pushForceAc = sqrt((Fx*Fx)+(Fy*Fy)); // Equation 2 using encoder to calcula te angle, this mimicks what SmartWheel does in their papers
   // float pushForceSum = (appliedForceN1) + (appliedForceN3) + (appliedForceN5); // Equation 3 using the tangential forces only
   // float pushForceBno = sqrt((FxBno*FxBno)+(FyBno*FyBno)); // Equation 4 using the BNO to get the wheel angle  
    float correctedPushForce = sqrt((correctedFx*correctedFx)+(correctedFy*correctedFy));
   // float correctedPushForceSum = correctedTan1 + correctedTan2 + correctedTan3;

      
    // Erfan's push force equation 
   // float Fmag = computeForceMagnitude(appliedForceN6,appliedForceN5,appliedForceN2,appliedForceN1,appliedForceN4,appliedForceN3);
    //float filteredFmag = computeForceMagnitude(filteredTangential1,filteredTangential2,filteredTangential3);

     if (dataFile) {
      digitalWrite(greenLed, HIGH);
      //digitalWrite(orangeLed,LOW);
      float t = (millis() - trialStartTime) / 1000.0f; // Reseting the time to zero
      dataFile.print(t,3); dataFile.print(","); // Time
      //dataFile.print(enVelocity, 6); dataFile.print(",");
      dataFile.print(appliedForceN5);dataFile.print(","); // Beam 1 Tangential
      dataFile.print(appliedForceN6);dataFile.print(","); // Beam 1 Radial
      dataFile.print(appliedForceN1);dataFile.print(","); // Beam 2 Tangential
      dataFile.print(appliedForceN2);dataFile.print(","); // Beam 2 Radial
      dataFile.print(appliedForceN3);dataFile.print(","); // Beam 3 Tangential
      dataFile.print(appliedForceN4);dataFile.print(","); // Beam 3 Radial 

   //   dataFile.print(filteredTangential1);dataFile.print(","); // Filtered Beam 1 Tangential
   //   dataFile.print(filteredTangential2);dataFile.print(","); // Filtered Beam 2 Tangential
  //    dataFile.print(filteredTangential3);dataFile.print(","); // Filtered Beam 3 Tangential
      //dataFile.print(wheelAngleDeg);dataFile.print(","); // Angle of wheel

      //dataFile.print(Fx);dataFile.print(","); // Calibrated
      //dataFile.print(Fy);dataFile.print(","); 
   //   dataFile.print(pushForceAc);dataFile.print(",");
  //    dataFile.print(pushForceSum);dataFile.print(",");
//      dataFile.print(pushForceS);dataFile.print(",");
//      dataFile.print(pushForceBno);dataFile.print(",");
   //   dataFile.print(correctedPushForceSum);dataFile.print(",");

      dataFile.print(correctedTan1); dataFile.print(",");
      dataFile.print(correctedTan2); dataFile.print(",");
      dataFile.print(correctedTan3); dataFile.print(",");
      //dataFile.print(wheelAngleDeg);dataFile.print(",");
      //dataFile.print(calibratedAngleDeg);dataFile.print(",");
     // dataFile.print(wheelSpeed);dataFile.print(",");
      //dataFile.print(distanceTraveled);dataFile.print(",");

      dataFile.print(bnoAngleX);dataFile.print(",");

    //  dataFile.print(quat.w());dataFile.print(",");
   //   dataFile.print(quat.x());dataFile.print(",");
   //   dataFile.print(quat.y());dataFile.print(",");
     // dataFile.print(quat.z());dataFile.print(",");

    //  dataFile.print(filteredAccel.x());dataFile.print(",");
    //  dataFile.print(filteredAccel.y());dataFile.print(",");
     // dataFile.print(filteredAccel.z());dataFile.print(",");

    //  dataFile.print(globalAccel.x());dataFile.print(",");
   //   dataFile.print(globalAccel.y());dataFile.print(",");
    //  dataFile.print(globalAccel.z());dataFile.print(",");

  //    dataFile.print(filteredAngAccel.x());dataFile.print(",");
    //  dataFile.print(filteredAngAccel.y());dataFile.print(",");
    //  dataFile.print(filteredAngAccel.z());dataFile.print(",");
//
     // dataFile.print(globalAngAccel.x());dataFile.print(",");
     // dataFile.print(globalAngAccel.y());dataFile.print(",");
      //dataFile.print(globalAngAccel.z());dataFile.print(",");
      dataFile.print(correctedPushForce);dataFile.print(",");

      dataFile.println(wheelSpeed);



      //dataFile.println(calibratedDistanceTraveled);


      dataFile.flush();
      } 
      else {
        Serial.println("⚠️ Data file is not open!");
      }
  }
}

int getTrialNumber() {
  int num = 1;
  File indexFile;
  if (SD.exists("/index.txt")) {
    indexFile = SD.open("/index.txt", FILE_READ);
    if (indexFile) {
      String content = indexFile.readStringUntil('\n');
      num = content.toInt() + 1;
      indexFile.close();
    }
  }
  indexFile = SD.open("/index.txt", FILE_WRITE);
  if (indexFile) {
    indexFile.println(num);
    indexFile.close();
  }
  return num;
}

