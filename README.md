# Guidelines
## SMARTWheel Rebuild
## Author 
  - <PERSON> (<EMAIL>)
## Purpose 
This project aims to replicate the SMARTWheel.
## Description 
Currently, only one of the SMARTWheels in the lab works; should it break, it is difficult to fix. This project aims to replicate the SMARTWheel's functionality of measuring the force a user applies to the push rim and the speed at which the wheel turns. The original optical encoder and as many original loadcells as possible will be used in the replication. The first stage of this project is to replicate the basic functionality of the loadcells and encoder. The second stage will entail how to process the data and if additional sensors should be added.
## Setup
Step-by-step list of what to do from installation to being able to run the program. Please include:
  - All dependencies, libraries, drivers etc. that you need to install
  - How to clone
  - How to build and run
  - How to edit code
  - Please add all relevant internet links for where to download drivers, libraries etc. 
## Required Hardware 
- For lab-built devices, please include a list of all the parts + relevant documentation links (e.g. link to the online documentation for the Feather M0)
- Feather m0
- 6 strain gauges (https://www.micro-measurements.com/pca/detail/s5232)
- 1 optical shaft encoder (https://www.usdigital.com/products/encoders/incremental/shaft/s1/)
- hx711 amplifier 
## Troubleshooting Items
  - Known issues 
