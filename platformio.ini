; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
lib_deps = 
	madhephaestus/ESP32Encoder@^0.11.7
	bogde/HX711@^0.7.5
	plerup/EspSoftwareSerial@^8.2.0
	andrewrapp/XBee@0.0.0-alpha+sha.39f236ea18
